<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="8dp"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@color/card_background"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Thumbnail Container -->
        <androidx.cardview.widget.CardView
            android:id="@+id/thumbnailContainer"
            android:layout_width="140dp"
            android:layout_height="90dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageViewThumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                tools:src="@drawable/ic_video_player" />

            <!-- Gradient Overlay -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/gradient_overlay" />

            <!-- Play Icon -->
            <ImageView
                android:id="@+id/imageViewPlayIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_play_arrow"
                android:background="@drawable/play_button_background"
                android:padding="10dp"
                app:tint="@color/white" />

            <!-- Duration Badge -->
            <TextView
                android:id="@+id/textViewDurationBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="8dp"
                android:background="@drawable/duration_background"
                android:padding="6dp"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:textStyle="bold"
                tools:text="00:04" />

        </androidx.cardview.widget.CardView>

        <!-- Video Title -->
        <TextView
            android:id="@+id/textViewVideoName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/thumbnailContainer"
            app:layout_constraintTop_toTopOf="@+id/thumbnailContainer"
            tools:text="اسم الفيديو الطويل جداً والذي قد يحتاج إلى سطرين" />

        <!-- Duration Info -->
        <TextView
            android:id="@+id/textViewDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:drawableStart="@drawable/ic_access_time"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/thumbnailContainer"
            app:layout_constraintTop_toBottomOf="@+id/textViewVideoName"
            tools:text="مدة الفيديو: 00:04" />

        <!-- File Size Info -->
        <TextView
            android:id="@+id/textViewSize"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:drawableStart="@drawable/ic_storage"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/thumbnailContainer"
            app:layout_constraintTop_toBottomOf="@+id/textViewDuration"
            tools:text="حجم الملف: 5.1 MB" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
