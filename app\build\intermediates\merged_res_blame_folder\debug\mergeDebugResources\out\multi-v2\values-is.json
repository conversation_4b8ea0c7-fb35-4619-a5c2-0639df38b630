{"logs": [{"outputFile": "com.videoplayer.app-mergeDebugResources-38:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d5e0ffba3d1002a1fc6a067c8f700aca\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "954,1054,1151,1263,1348,1449,1563,1644,1723,1814,1907,2000,2094,2200,2293,2388,2483,2574,2668,2749,2859,2966,3063,3172,3272,3375,3530,13458", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "1049,1146,1258,1343,1444,1558,1639,1718,1809,1902,1995,2089,2195,2288,2383,2478,2569,2663,2744,2854,2961,3058,3167,3267,3370,3525,3623,13534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fd47ea89dff03665557e3b19b314124\\transformed\\material-1.10.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2774,2861,2940,2997,3052,3118,3194,3274,3363,3439,3506,3580,3650,3716,3818,3904,3974,4065,4155,4229,4302,4391,4442,4523,4595,4676,4762,4824,4888,4951,5020,5134,5240,5348,5450,5511,5570", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2769,2856,2935,2992,3047,3113,3189,3269,3358,3434,3501,3575,3645,3711,3813,3899,3969,4060,4150,4224,4297,4386,4437,4518,4590,4671,4757,4819,4883,4946,5015,5129,5235,5343,5445,5506,5565,5645"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3628,3702,3774,3853,3935,4732,4829,4944,5026,8824,8912,8976,9037,9127,9191,9254,9316,9384,9448,9504,9627,9692,9754,9810,9881,10008,10092,10176,10312,10389,10466,10582,10669,10748,10805,10860,10926,11002,11082,11171,11247,11314,11388,11458,11524,11626,11712,11782,11873,11963,12037,12110,12199,12250,12331,12403,12484,12570,12632,12696,12759,12828,12942,13048,13156,13258,13319,13378", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "949,3697,3769,3848,3930,4010,4824,4939,5021,5086,8907,8971,9032,9122,9186,9249,9311,9379,9443,9499,9622,9687,9749,9805,9876,10003,10087,10171,10307,10384,10461,10577,10664,10743,10800,10855,10921,10997,11077,11166,11242,11309,11383,11453,11519,11621,11707,11777,11868,11958,12032,12105,12194,12245,12326,12398,12479,12565,12627,12691,12754,12823,12937,13043,13151,13253,13314,13373,13453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f1b32c0fc18016c5a3753e707e390b71\\transformed\\core-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4015,4110,4217,4314,4414,4517,4621,13539", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "4105,4212,4309,4409,4512,4616,4727,13635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74c8f557fa6abfe04d4cbe84bc441f56\\transformed\\exoplayer-ui-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3409,3479,3531,3591,3665,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3404,3474,3526,3586,3660,3734,3787"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,583,5091,5176,5260,5337,5426,5523,5592,5656,5747,5838,5901,5965,6027,6095,6219,6345,6469,6544,6625,6698,6767,6850,6932,6997,7707,7760,7821,7871,7932,7991,8061,8124,8186,8250,8310,8376,8441,8511,8563,8623,8697,8771", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "378,578,779,5171,5255,5332,5421,5518,5587,5651,5742,5833,5896,5960,6022,6090,6214,6340,6464,6539,6620,6693,6762,6845,6927,6992,7072,7755,7816,7866,7927,7986,8056,8119,8181,8245,8305,8371,8436,8506,8558,8618,8692,8766,8819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f6658a502d56b73d797878a8e950660d\\transformed\\exoplayer-core-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7077,7144,7203,7262,7328,7404,7467,7556,7638", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "7139,7198,7257,7323,7399,7462,7551,7633,7702"}}]}]}