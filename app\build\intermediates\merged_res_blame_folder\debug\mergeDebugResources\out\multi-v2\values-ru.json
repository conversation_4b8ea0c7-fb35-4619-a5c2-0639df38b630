{"logs": [{"outputFile": "com.videoplayer.app-mergeDebugResources-38:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fd47ea89dff03665557e3b19b314124\\transformed\\material-1.10.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1044,4042,4120,4198,4282,4380,5198,5295,5432,5524,9460,9559,9636,9699,9817,9878,9943,10000,10070,10131,10185,10301,10358,10420,10474,10548,10676,10764,10850,10987,11071,11156,11290,11381,11457,11511,11562,11628,11700,11778,11874,11956,12036,12112,12189,12266,12373,12462,12535,12625,12720,12794,12875,12968,13023,13104,13170,13256,13341,13403,13467,13530,13602,13700,13799,13894,13986,14044,14099", "endLines": "28,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "1320,4115,4193,4277,4375,4466,5290,5427,5519,5585,9554,9631,9694,9812,9873,9938,9995,10065,10126,10180,10296,10353,10415,10469,10543,10671,10759,10845,10982,11066,11151,11285,11376,11452,11506,11557,11623,11695,11773,11869,11951,12031,12107,12184,12261,12368,12457,12530,12620,12715,12789,12870,12963,13018,13099,13165,13251,13336,13398,13462,13525,13597,13695,13794,13889,13981,14039,14094,14174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d5e0ffba3d1002a1fc6a067c8f700aca\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1440,1542,1641,1727,1832,1953,2032,2108,2200,2294,2389,2482,2577,2671,2767,2862,2954,3046,3135,3241,3348,3446,3555,3662,3776,3942,14179", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "1435,1537,1636,1722,1827,1948,2027,2103,2195,2289,2384,2477,2572,2666,2762,2857,2949,3041,3130,3236,3343,3441,3550,3657,3771,3937,4037,14256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74c8f557fa6abfe04d4cbe84bc441f56\\transformed\\exoplayer-ui-2.19.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3740,3806,3858,3919,4004,4089", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3735,3801,3853,3914,3999,4084,4147"}, "to": {"startLines": "2,11,17,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,375,714,5590,5673,5756,5839,5929,6029,6100,6173,6272,6373,6446,6518,6583,6661,6773,6884,7001,7078,7173,7245,7318,7406,7494,7563,8295,8348,8410,8458,8519,8586,8654,8720,8802,8860,8917,8983,9048,9114,9166,9227,9312,9397", "endLines": "10,16,22,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "370,709,1039,5668,5751,5834,5924,6024,6095,6168,6267,6368,6441,6513,6578,6656,6768,6879,6996,7073,7168,7240,7313,7401,7489,7558,7623,8343,8405,8453,8514,8581,8649,8715,8797,8855,8912,8978,9043,9109,9161,9222,9307,9392,9455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f6658a502d56b73d797878a8e950660d\\transformed\\exoplayer-core-2.19.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7628,7700,7761,7826,7892,7970,8044,8132,8218", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "7695,7756,7821,7887,7965,8039,8127,8213,8290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f1b32c0fc18016c5a3753e707e390b71\\transformed\\core-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "61,62,63,64,65,66,67,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4471,4569,4671,4772,4873,4978,5081,14261", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4564,4666,4767,4868,4973,5076,5193,14357"}}]}]}