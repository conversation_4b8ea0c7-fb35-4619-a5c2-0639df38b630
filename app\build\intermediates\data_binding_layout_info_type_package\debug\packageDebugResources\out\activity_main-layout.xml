<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.videoplayer.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="61" endOffset="51"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/recyclerViewVideos" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="26" startOffset="4" endLine="36" endOffset="45"/></Target><Target id="@+id/textViewNoVideos" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="49" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="51" startOffset="4" endLine="59" endOffset="51"/></Target></Targets></Layout>