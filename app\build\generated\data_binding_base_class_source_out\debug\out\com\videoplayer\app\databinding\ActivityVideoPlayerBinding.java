// Generated by view binder compiler. Do not edit!
package com.videoplayer.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.exoplayer2.ui.PlayerView;
import com.videoplayer.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVideoPlayerBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final PlayerView playerView;

  @NonNull
  public final ProgressBar progressBarLoading;

  @NonNull
  public final TextView textViewError;

  private ActivityVideoPlayerBinding(@NonNull ConstraintLayout rootView,
      @NonNull PlayerView playerView, @NonNull ProgressBar progressBarLoading,
      @NonNull TextView textViewError) {
    this.rootView = rootView;
    this.playerView = playerView;
    this.progressBarLoading = progressBarLoading;
    this.textViewError = textViewError;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVideoPlayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVideoPlayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_video_player, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVideoPlayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.playerView;
      PlayerView playerView = ViewBindings.findChildViewById(rootView, id);
      if (playerView == null) {
        break missingId;
      }

      id = R.id.progressBarLoading;
      ProgressBar progressBarLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressBarLoading == null) {
        break missingId;
      }

      id = R.id.textViewError;
      TextView textViewError = ViewBindings.findChildViewById(rootView, id);
      if (textViewError == null) {
        break missingId;
      }

      return new ActivityVideoPlayerBinding((ConstraintLayout) rootView, playerView,
          progressBarLoading, textViewError);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
