{"logs": [{"outputFile": "com.videoplayer.app-mergeDebugResources-38:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74c8f557fa6abfe04d4cbe84bc441f56\\transformed\\exoplayer-ui-2.19.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3463,3529,3581,3642,3717,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3458,3524,3576,3637,3712,3787,3840"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,563,5120,5206,5301,5384,5482,5581,5656,5724,5825,5926,5991,6054,6117,6189,6317,6449,6576,6653,6727,6800,6875,6965,7064,7133,7887,7940,8000,8048,8109,8170,8241,8301,8369,8432,8497,8563,8627,8693,8745,8806,8881,8956", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "375,558,734,5201,5296,5379,5477,5576,5651,5719,5820,5921,5986,6049,6112,6184,6312,6444,6571,6648,6722,6795,6870,6960,7059,7128,7194,7935,7995,8043,8104,8165,8236,8296,8364,8427,8492,8558,8622,8688,8740,8801,8876,8951,9004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f1b32c0fc18016c5a3753e707e390b71\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4014,4116,4218,4319,4419,4527,4631,13840", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "4111,4213,4314,4414,4522,4626,4745,13936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d5e0ffba3d1002a1fc6a067c8f700aca\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1027,1122,1222,1304,1404,1521,1606,1684,1775,1868,1963,2057,2151,2244,2339,2434,2525,2617,2701,2811,2917,3017,3125,3231,3333,3494,13756", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "1022,1117,1217,1299,1399,1516,1601,1679,1770,1863,1958,2052,2146,2239,2334,2429,2520,2612,2696,2806,2912,3012,3120,3226,3328,3489,3588,13835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f6658a502d56b73d797878a8e950660d\\transformed\\exoplayer-core-2.19.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7199,7264,7330,7400,7464,7543,7611,7713,7807", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "7259,7325,7395,7459,7538,7606,7708,7802,7882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fd47ea89dff03665557e3b19b314124\\transformed\\material-1.10.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "739,3593,3670,3750,3833,3927,4750,4845,4972,5056,9009,9112,9182,9249,9358,9421,9488,9547,9621,9684,9738,9853,9911,9973,10027,10102,10231,10321,10410,10551,10633,10715,10854,10940,11024,11084,11135,11201,11274,11352,11438,11519,11591,11668,11743,11814,11915,12009,12088,12184,12278,12352,12428,12514,12567,12654,12720,12805,12896,12958,13022,13085,13154,13256,13357,13453,13554,13618,13673", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "917,3665,3745,3828,3922,4009,4840,4967,5051,5115,9107,9177,9244,9353,9416,9483,9542,9616,9679,9733,9848,9906,9968,10022,10097,10226,10316,10405,10546,10628,10710,10849,10935,11019,11079,11130,11196,11269,11347,11433,11514,11586,11663,11738,11809,11910,12004,12083,12179,12273,12347,12423,12509,12562,12649,12715,12800,12891,12953,13017,13080,13149,13251,13352,13448,13549,13613,13668,13751"}}]}]}