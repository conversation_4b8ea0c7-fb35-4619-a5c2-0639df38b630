1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoplayer.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->D:\ooo\app\src\main\AndroidManifest.xml:5:5-80
11-->D:\ooo\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
12-->D:\ooo\app\src\main\AndroidManifest.xml:6:5-75
12-->D:\ooo\app\src\main\AndroidManifest.xml:6:22-72
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\ooo\app\src\main\AndroidManifest.xml:7:5-67
13-->D:\ooo\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->D:\ooo\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\ooo\app\src\main\AndroidManifest.xml:8:22-76
15
16    <permission
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.videoplayer.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.videoplayer.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\ooo\app\src\main\AndroidManifest.xml:10:5-38:19
23        android:allowBackup="true"
23-->D:\ooo\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1b32c0fc18016c5a3753e707e390b71\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\ooo\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\ooo\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\ooo\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->D:\ooo\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\ooo\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->D:\ooo\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.VideoPlayer" >
33-->D:\ooo\app\src\main\AndroidManifest.xml:18:9-49
34        <activity
34-->D:\ooo\app\src\main\AndroidManifest.xml:21:9-29:20
35            android:name="com.videoplayer.app.MainActivity"
35-->D:\ooo\app\src\main\AndroidManifest.xml:22:13-41
36            android:exported="true"
36-->D:\ooo\app\src\main\AndroidManifest.xml:23:13-36
37            android:theme="@style/Theme.VideoPlayer" >
37-->D:\ooo\app\src\main\AndroidManifest.xml:24:13-53
38            <intent-filter>
38-->D:\ooo\app\src\main\AndroidManifest.xml:25:13-28:29
39                <action android:name="android.intent.action.MAIN" />
39-->D:\ooo\app\src\main\AndroidManifest.xml:26:17-69
39-->D:\ooo\app\src\main\AndroidManifest.xml:26:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->D:\ooo\app\src\main\AndroidManifest.xml:27:17-77
41-->D:\ooo\app\src\main\AndroidManifest.xml:27:27-74
42            </intent-filter>
43        </activity>
44        <activity
44-->D:\ooo\app\src\main\AndroidManifest.xml:31:9-36:67
45            android:name="com.videoplayer.app.VideoPlayerActivity"
45-->D:\ooo\app\src\main\AndroidManifest.xml:32:13-48
46            android:configChanges="orientation|screenSize|keyboardHidden"
46-->D:\ooo\app\src\main\AndroidManifest.xml:34:13-74
47            android:exported="false"
47-->D:\ooo\app\src\main\AndroidManifest.xml:33:13-37
48            android:screenOrientation="landscape"
48-->D:\ooo\app\src\main\AndroidManifest.xml:35:13-50
49            android:theme="@style/Theme.VideoPlayer.Fullscreen" />
49-->D:\ooo\app\src\main\AndroidManifest.xml:36:13-64
50
51        <provider
51-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.videoplayer.app.androidx-startup"
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b80eea62a639290f87c265015aebc47c\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\343d86a04ad23504867c3c38499fb70c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\343d86a04ad23504867c3c38499fb70c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\343d86a04ad23504867c3c38499fb70c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <receiver
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
67            android:name="androidx.profileinstaller.ProfileInstallReceiver"
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
68            android:directBootAware="false"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
69            android:enabled="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
70            android:exported="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
71            android:permission="android.permission.DUMP" >
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
73                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
76                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
79                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
82                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1278f979de6fdada0d3da85ec021b833\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
83            </intent-filter>
84        </receiver>
85    </application>
86
87</manifest>
