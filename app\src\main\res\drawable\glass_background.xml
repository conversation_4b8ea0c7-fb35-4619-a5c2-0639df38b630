<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Glass background with transparency -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#35FFFFFF"
                android:centerColor="#25FFFFFF"
                android:endColor="#20FFFFFF"
                android:angle="135" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1.5dp"
                android:color="#50FFFFFF" />
        </shape>
    </item>
    
    <!-- Inner glow effect -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#20FFFFFF"
                android:centerColor="#10FFFFFF"
                android:endColor="#05FFFFFF"
                android:angle="45" />
            <corners android:radius="15dp" />
        </shape>
    </item>
    
</layer-list>
