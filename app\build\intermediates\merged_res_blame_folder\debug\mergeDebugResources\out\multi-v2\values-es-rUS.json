{"logs": [{"outputFile": "com.videoplayer.app-mergeDebugResources-38:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fd47ea89dff03665557e3b19b314124\\transformed\\material-1.10.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1103,1198,1268,1331,1424,1488,1560,1623,1697,1761,1817,1935,1993,2055,2111,2191,2325,2414,2495,2636,2717,2797,2948,3038,3115,3171,3227,3293,3369,3451,3539,3628,3701,3778,3848,3925,4031,4120,4194,4288,4390,4462,4543,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5402,5513,5615,5720,5780,5840", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "273,355,435,521,626,722,824,952,1033,1098,1193,1263,1326,1419,1483,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2320,2409,2490,2631,2712,2792,2943,3033,3110,3166,3222,3288,3364,3446,3534,3623,3696,3773,3843,3920,4026,4115,4189,4283,4385,4457,4538,4642,4695,4780,4847,4940,5029,5091,5155,5218,5286,5397,5508,5610,5715,5775,5835,5918"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "757,3651,3733,3813,3899,4004,4832,4934,5062,5143,8997,9092,9162,9225,9318,9382,9454,9517,9591,9655,9711,9829,9887,9949,10005,10085,10219,10308,10389,10530,10611,10691,10842,10932,11009,11065,11121,11187,11263,11345,11433,11522,11595,11672,11742,11819,11925,12014,12088,12182,12284,12356,12437,12541,12594,12679,12746,12839,12928,12990,13054,13117,13185,13296,13407,13509,13614,13674,13734", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "930,3728,3808,3894,3999,4095,4929,5057,5138,5203,9087,9157,9220,9313,9377,9449,9512,9586,9650,9706,9824,9882,9944,10000,10080,10214,10303,10384,10525,10606,10686,10837,10927,11004,11060,11116,11182,11258,11340,11428,11517,11590,11667,11737,11814,11920,12009,12083,12177,12279,12351,12432,12536,12589,12674,12741,12834,12923,12985,13049,13112,13180,13291,13402,13504,13609,13669,13729,13812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f6658a502d56b73d797878a8e950660d\\transformed\\exoplayer-core-2.19.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7187,7262,7325,7390,7459,7536,7610,7699,7787", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "7257,7320,7385,7454,7531,7605,7694,7782,7851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f1b32c0fc18016c5a3753e707e390b71\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4199,4301,4401,4499,4606,4712,13900", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4194,4296,4396,4494,4601,4707,4827,13996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d5e0ffba3d1002a1fc6a067c8f700aca\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1055,1164,1272,1357,1459,1575,1660,1740,1831,1924,2019,2113,2212,2305,2404,2500,2591,2682,2764,2871,2970,3069,3177,3285,3392,3551,13817", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "1050,1159,1267,1352,1454,1570,1655,1735,1826,1919,2014,2108,2207,2300,2399,2495,2586,2677,2759,2866,2965,3064,3172,3280,3387,3546,3646,13895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74c8f557fa6abfe04d4cbe84bc441f56\\transformed\\exoplayer-ui-2.19.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3382,3448,3501,3566,3645,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3377,3443,3496,3561,3640,3719,3777"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,570,5208,5294,5381,5454,5550,5646,5726,5794,5893,5992,6058,6127,6193,6264,6359,6454,6549,6620,6704,6780,6860,6958,7057,7123,7856,7909,7967,8015,8076,8141,8203,8269,8341,8405,8466,8532,8597,8663,8716,8781,8860,8939", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "375,565,752,5289,5376,5449,5545,5641,5721,5789,5888,5987,6053,6122,6188,6259,6354,6449,6544,6615,6699,6775,6855,6953,7052,7118,7182,7904,7962,8010,8071,8136,8198,8264,8336,8400,8461,8527,8592,8658,8711,8776,8855,8934,8992"}}]}]}