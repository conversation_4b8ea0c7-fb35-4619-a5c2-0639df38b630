<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Main glass background -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#40FFFFFF"
                android:centerColor="#30FFFFFF"
                android:endColor="#25FFFFFF"
                android:angle="135" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1.5dp"
                android:color="#60FFFFFF" />
        </shape>
    </item>
    
    <!-- Top reflection highlight -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:height="20dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#50FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="90" />
            <corners 
                android:topLeftRadius="14dp"
                android:topRightRadius="14dp" />
        </shape>
    </item>
    
    <!-- Inner glow -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#20FFFFFF"
                android:centerColor="#15FFFFFF"
                android:endColor="#10FFFFFF"
                android:angle="45" />
            <corners android:radius="15dp" />
        </shape>
    </item>
    
</layer-list>
