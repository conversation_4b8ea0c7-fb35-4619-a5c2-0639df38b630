package com.videoplayer.app

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.videoplayer.app.adapter.VideoAdapter
import com.videoplayer.app.databinding.ActivityMainBinding
import com.videoplayer.app.model.VideoItem
import com.videoplayer.app.utils.VideoManager
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var videoAdapter: VideoAdapter
    private lateinit var videoManager: VideoManager

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            loadVideos()
        } else {
            showNoPermissionMessage()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupRecyclerView()
        videoManager = VideoManager(this)
        
        checkPermissionAndLoadVideos()
    }

    private fun setupRecyclerView() {
        videoAdapter = VideoAdapter { video ->
            openVideoPlayer(video)
        }
        
        binding.recyclerViewVideos.apply {
            adapter = videoAdapter
            layoutManager = LinearLayoutManager(this@MainActivity)
        }
    }

    private fun checkPermissionAndLoadVideos() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_VIDEO
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }

        when {
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED -> {
                loadVideos()
            }
            else -> {
                requestPermissionLauncher.launch(permission)
            }
        }
    }

    private fun loadVideos() {
        binding.progressBar.visibility = View.VISIBLE
        binding.textViewNoVideos.visibility = View.GONE
        
        lifecycleScope.launch {
            try {
                val videos = videoManager.getAllVideos()
                
                if (videos.isEmpty()) {
                    showNoVideosMessage()
                } else {
                    showVideos(videos)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                showNoVideosMessage()
            } finally {
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun showVideos(videos: List<VideoItem>) {
        binding.recyclerViewVideos.visibility = View.VISIBLE
        binding.textViewNoVideos.visibility = View.GONE
        videoAdapter.submitList(videos)
    }

    private fun showNoVideosMessage() {
        binding.recyclerViewVideos.visibility = View.GONE
        binding.textViewNoVideos.visibility = View.VISIBLE
        binding.textViewNoVideos.text = getString(R.string.no_videos_found)
    }

    private fun showNoPermissionMessage() {
        binding.recyclerViewVideos.visibility = View.GONE
        binding.textViewNoVideos.visibility = View.VISIBLE
        binding.textViewNoVideos.text = getString(R.string.permission_required)
    }

    private fun openVideoPlayer(video: VideoItem) {
        val intent = Intent(this, VideoPlayerActivity::class.java).apply {
            putExtra(VideoPlayerActivity.EXTRA_VIDEO_ITEM, video)
        }
        startActivity(intent)
    }
}
