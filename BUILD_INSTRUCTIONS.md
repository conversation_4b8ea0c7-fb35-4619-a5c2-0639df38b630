# تعليمات البناء والتشغيل - Build Instructions

## المتطلبات الأساسية

### 1. تثبيت Android Studio
- حمل وثبت [Android Studio](https://developer.android.com/studio) أحدث إصدار
- تأكد من تثبيت Android SDK (API 24 أو أحدث)

### 2. تثبيت Java Development Kit (JDK)
- JDK 8 أو أحدث
- يمكن تحميله من [Oracle](https://www.oracle.com/java/technologies/downloads/) أو [OpenJDK](https://openjdk.org/)

## خطوات البناء

### الطريقة الأولى: باستخدام Android Studio

1. **فتح المشروع:**
   ```
   - افتح Android Studio
   - اختر "Open an existing project"
   - حدد مجلد المشروع (VideoPlayer)
   ```

2. **مزامنة المشروع:**
   ```
   - انتظر حتى يتم تحميل المشروع
   - اضغط على "Sync Now" إذا ظهرت الرسالة
   ```

3. **بناء التطبيق:**
   ```
   - من قائمة Build اختر "Make Project"
   - أو استخدم الاختصار: Ctrl+F9 (Windows/Linux) أو Cmd+F9 (Mac)
   ```

4. **تشغيل التطبيق:**
   ```
   - اربط جهاز Android أو ابدأ محاكي
   - اضغط على زر "Run" أو استخدم Shift+F10
   ```

### الطريقة الثانية: باستخدام سطر الأوامر

1. **التنقل إلى مجلد المشروع:**
   ```bash
   cd path/to/VideoPlayer
   ```

2. **بناء التطبيق:**
   ```bash
   # Windows
   gradlew.bat assembleDebug
   
   # Linux/Mac
   ./gradlew assembleDebug
   ```

3. **تثبيت التطبيق على الجهاز:**
   ```bash
   # Windows
   gradlew.bat installDebug
   
   # Linux/Mac
   ./gradlew installDebug
   ```

## إعداد الجهاز للتطوير

### تفعيل خيارات المطور:
1. اذهب إلى الإعدادات > حول الهاتف
2. اضغط على "رقم البناء" 7 مرات
3. ارجع للإعدادات > خيارات المطور
4. فعل "تصحيح USB"

### ربط الجهاز:
1. اربط الجهاز بالكمبيوتر عبر USB
2. اقبل رسالة "السماح بتصحيح USB"
3. تحقق من ظهور الجهاز في Android Studio

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ "SDK not found":**
   ```
   - تأكد من تثبيت Android SDK
   - حدث ملف local.properties بمسار SDK الصحيح
   ```

2. **خطأ في الصلاحيات:**
   ```
   - تأكد من منح صلاحية "الوصول للملفات" للتطبيق
   - في الإعدادات > التطبيقات > مشغل الفيديو > الصلاحيات
   ```

3. **مشاكل في تشغيل الفيديو:**
   ```
   - تأكد من وجود فيديوهات على الجهاز
   - تحقق من تنسيق الفيديو المدعوم
   ```

4. **خطأ في البناء:**
   ```bash
   # تنظيف المشروع
   gradlew.bat clean
   gradlew.bat build
   ```

## ملفات APK

بعد البناء الناجح، ستجد ملف APK في:
```
app/build/outputs/apk/debug/app-debug.apk
```

يمكنك تثبيت هذا الملف مباشرة على أي جهاز Android.

## اختبار التطبيق

### اختبارات أساسية:
1. تشغيل التطبيق بنجاح
2. طلب الصلاحيات عند أول تشغيل
3. عرض قائمة الفيديوهات
4. تشغيل فيديو واختبار عناصر التحكم
5. التنقل بين الشاشات

### اختبارات متقدمة:
1. اختبار على أجهزة مختلفة
2. اختبار تنسيقات فيديو متعددة
3. اختبار الأداء مع فيديوهات كبيرة
4. اختبار دوران الشاشة

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md
2. راجع رسائل الخطأ في Android Studio
3. تأكد من تحديث جميع المكونات
