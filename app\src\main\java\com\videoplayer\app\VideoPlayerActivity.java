package com.videoplayer.app;

import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.videoplayer.app.databinding.ActivityVideoPlayerBinding;
import com.videoplayer.app.model.VideoItem;

public class VideoPlayerActivity extends AppCompatActivity {

    public static final String EXTRA_VIDEO_ITEM = "extra_video_item";

    private ActivityVideoPlayerBinding binding;
    private ExoPlayer exoPlayer;
    private VideoItem videoItem;
    private long playbackPosition = 0L;
    private boolean playWhenReady = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityVideoPlayerBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Keep screen on during video playback
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        // Hide system UI for immersive experience
        hideSystemUI();

        videoItem = getIntent().getParcelableExtra(EXTRA_VIDEO_ITEM);

        if (videoItem == null) {
            finish();
            return;
        }

        setupPlayer();
    }

    private void hideSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        WindowInsetsControllerCompat windowInsetsController =
            WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        if (windowInsetsController != null) {
            windowInsetsController.setSystemBarsBehavior(
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE);
            windowInsetsController.hide(WindowInsetsCompat.Type.systemBars());
        }
    }

    private void setupPlayer() {
        exoPlayer = new ExoPlayer.Builder(this).build();
        binding.playerView.setPlayer(exoPlayer);
        
        MediaItem mediaItem = MediaItem.fromUri(Uri.parse(videoItem.getPath()));
        MediaSource mediaSource = buildMediaSource(mediaItem);
        
        exoPlayer.setMediaSource(mediaSource);
        exoPlayer.setPlayWhenReady(playWhenReady);
        exoPlayer.seekTo(playbackPosition);
        exoPlayer.prepare();

        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_BUFFERING:
                        binding.progressBarLoading.setVisibility(View.VISIBLE);
                        break;
                    case Player.STATE_READY:
                        binding.progressBarLoading.setVisibility(View.GONE);
                        binding.textViewError.setVisibility(View.GONE);
                        break;
                    case Player.STATE_ENDED:
                        // Video ended, you can add logic here
                        break;
                }
            }

            @Override
            public void onPlayerError(PlaybackException error) {
                binding.progressBarLoading.setVisibility(View.GONE);
                binding.textViewError.setVisibility(View.VISIBLE);
            }
        });
    }

    private MediaSource buildMediaSource(MediaItem mediaItem) {
        DefaultDataSource.Factory dataSourceFactory = new DefaultDataSource.Factory(this);
        return new ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(mediaItem);
    }

    private void releasePlayer() {
        if (exoPlayer != null) {
            playbackPosition = exoPlayer.getCurrentPosition();
            playWhenReady = exoPlayer.getPlayWhenReady();
            exoPlayer.release();
            exoPlayer = null;
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        hideSystemUI();
        if (exoPlayer == null) {
            setupPlayer();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        hideSystemUI();
        if (exoPlayer == null) {
            setupPlayer();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        releasePlayer();
    }

    @Override
    protected void onStop() {
        super.onStop();
        releasePlayer();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        releasePlayer();
    }
}
