# دليل البدء السريع - Quick Start Guide

## 🚀 تشغيل سريع

### 1. متطلبات أساسية
- Android Studio 4.0 أو أحدث
- Android SDK API 24+
- جهاز Android أو محاكي

### 2. خطوات سريعة
```bash
# 1. فتح المشروع في Android Studio
# 2. انتظار مزامنة Gradle
# 3. تشغيل التطبيق (Shift+F10)
```

## 📱 ميزات التطبيق

### الشاشة الرئيسية
- ✅ عرض قائمة الفيديوهات
- ✅ صور مصغرة للفيديوهات  
- ✅ معلومات الفيديو (المدة، الحجم)
- ✅ واجهة عربية

### شاشة التشغيل
- ✅ تشغيل فيديو بجودة عالية
- ✅ عناصر تحكم كاملة
- ✅ دعم ملء الشاشة
- ✅ دعم تنسيقات متعددة

## 🔧 هيكل الكود

```
MainActivity.kt          → الشاشة الرئيسية وقائمة الفيديوهات
VideoPlayerActivity.kt   → شاشة تشغيل الفيديو
VideoAdapter.kt         → محول قائمة الفيديوهات
VideoItem.kt           → نموذج بيانات الفيديو
VideoManager.kt        → إدارة الفيديوهات من النظام
```

## 🎯 نقاط مهمة

### الصلاحيات
```kotlin
// Android 13+
READ_MEDIA_VIDEO

// Android 12 وأقل  
READ_EXTERNAL_STORAGE
```

### المكتبات الرئيسية
```gradle
ExoPlayer 2.19.1    → تشغيل الفيديو
Glide 4.16.0       → تحميل الصور
Material Design    → التصميم
```

## 🐛 استكشاف أخطاء سريع

| المشكلة | الحل |
|---------|------|
| لا تظهر فيديوهات | تحقق من الصلاحيات |
| خطأ في التشغيل | تحقق من تنسيق الفيديو |
| تطبيق لا يعمل | تنظيف وإعادة بناء |

## 📝 تخصيص سريع

### تغيير الألوان
```xml
<!-- res/values/colors.xml -->
<color name="purple_500">#FF6200EE</color>
```

### تغيير النصوص
```xml
<!-- res/values/strings.xml -->
<string name="app_name">اسم جديد</string>
```

### إضافة ميزات جديدة
1. أضف التخطيط في `res/layout/`
2. أضف الكود في `java/com/videoplayer/app/`
3. حدث `AndroidManifest.xml` إذا لزم الأمر

## 🚀 نشر التطبيق

### بناء APK للإنتاج
```bash
gradlew assembleRelease
```

### موقع ملف APK
```
app/build/outputs/apk/release/app-release.apk
```

## 📞 دعم سريع

- 📖 راجع `README.md` للتفاصيل الكاملة
- 🔧 راجع `BUILD_INSTRUCTIONS.md` لمشاكل البناء
- 💡 تحقق من التعليقات في الكود للفهم العميق
