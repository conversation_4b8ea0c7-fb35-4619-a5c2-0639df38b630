<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Main gradient background -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/primary_blue"
                android:centerColor="@color/primary_blue_dark"
                android:endColor="@color/accent_orange"
                android:type="linear" />
        </shape>
    </item>

    <!-- Glass overlay for transparency effect -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#15FFFFFF"
                android:centerColor="#25FFFFFF"
                android:endColor="#15FFFFFF"
                android:angle="45" />
        </shape>
    </item>

</layer-list>
