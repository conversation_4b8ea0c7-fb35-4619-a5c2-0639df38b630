<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_video" modulePackage="com.videoplayer.app" filePath="app\src\main\res\layout\item_video.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_video_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="71" endOffset="51"/></Target><Target id="@+id/imageViewThumbnail" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="58"/></Target><Target id="@+id/textViewVideoName" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="39" endOffset="50"/></Target><Target id="@+id/textViewDuration" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="53" endOffset="45"/></Target><Target id="@+id/textViewSize" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="67" endOffset="45"/></Target></Targets></Layout>