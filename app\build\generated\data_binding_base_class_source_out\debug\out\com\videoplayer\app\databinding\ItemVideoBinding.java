// Generated by view binder compiler. Do not edit!
package com.videoplayer.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.videoplayer.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemVideoBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView imageViewThumbnail;

  @NonNull
  public final TextView textViewDuration;

  @NonNull
  public final TextView textViewSize;

  @NonNull
  public final TextView textViewVideoName;

  private ItemVideoBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView imageViewThumbnail, @NonNull TextView textViewDuration,
      @NonNull TextView textViewSize, @NonNull TextView textViewVideoName) {
    this.rootView = rootView;
    this.imageViewThumbnail = imageViewThumbnail;
    this.textViewDuration = textViewDuration;
    this.textViewSize = textViewSize;
    this.textViewVideoName = textViewVideoName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVideoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVideoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_video, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVideoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageViewThumbnail;
      ImageView imageViewThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (imageViewThumbnail == null) {
        break missingId;
      }

      id = R.id.textViewDuration;
      TextView textViewDuration = ViewBindings.findChildViewById(rootView, id);
      if (textViewDuration == null) {
        break missingId;
      }

      id = R.id.textViewSize;
      TextView textViewSize = ViewBindings.findChildViewById(rootView, id);
      if (textViewSize == null) {
        break missingId;
      }

      id = R.id.textViewVideoName;
      TextView textViewVideoName = ViewBindings.findChildViewById(rootView, id);
      if (textViewVideoName == null) {
        break missingId;
      }

      return new ItemVideoBinding((MaterialCardView) rootView, imageViewThumbnail, textViewDuration,
          textViewSize, textViewVideoName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
