<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <ImageView
            android:id="@+id/imageViewThumbnail"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:background="@color/light_gray"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_video_player" />

        <ImageView
            android:id="@+id/imageViewPlayIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_play_arrow"
            android:background="@drawable/play_button_background"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageViewThumbnail"
            app:layout_constraintEnd_toEndOf="@+id/imageViewThumbnail"
            app:layout_constraintStart_toStartOf="@+id/imageViewThumbnail"
            app:layout_constraintTop_toTopOf="@+id/imageViewThumbnail"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/textViewDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:background="@drawable/duration_background"
            android:padding="4dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/imageViewThumbnail"
            app:layout_constraintEnd_toEndOf="@+id/imageViewThumbnail"
            tools:text="00:04" />

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageViewThumbnail"
            tools:text="اسم الفيديو" />

        <TextView
            android:id="@+id/textViewSize"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/gray"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textViewTitle"
            tools:text="5.1 MB" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
