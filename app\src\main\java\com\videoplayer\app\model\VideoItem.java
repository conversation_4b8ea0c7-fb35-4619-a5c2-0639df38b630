package com.videoplayer.app.model;

import android.os.Parcel;
import android.os.Parcelable;

public class VideoItem implements Parcelable {
    private long id;
    private String name;
    private String path;
    private long duration; // in milliseconds
    private long size; // in bytes
    private long dateAdded;
    private String mimeType;

    public VideoItem(long id, String name, String path, long duration, long size, long dateAdded, String mimeType) {
        this.id = id;
        this.name = name;
        this.path = path;
        this.duration = duration;
        this.size = size;
        this.dateAdded = dateAdded;
        this.mimeType = mimeType;
    }

    protected VideoItem(Parcel in) {
        id = in.readLong();
        name = in.readString();
        path = in.readString();
        duration = in.readLong();
        size = in.readLong();
        dateAdded = in.readLong();
        mimeType = in.readString();
    }

    public static final Creator<VideoItem> CREATOR = new Creator<VideoItem>() {
        @Override
        public VideoItem createFromParcel(Parcel in) {
            return new VideoItem(in);
        }

        @Override
        public VideoItem[] newArray(int size) {
            return new VideoItem[size];
        }
    };

    public String getFormattedDuration() {
        long seconds = duration / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02d:%02d", minutes, seconds % 60);
        }
    }
    
    public String getFormattedSize() {
        double kb = size / 1024.0;
        double mb = kb / 1024.0;
        double gb = mb / 1024.0;
        
        if (gb >= 1) {
            return String.format("%.1f GB", gb);
        } else if (mb >= 1) {
            return String.format("%.1f MB", mb);
        } else {
            return String.format("%.1f KB", kb);
        }
    }

    // Getters
    public long getId() { return id; }
    public String getName() { return name; }
    public String getPath() { return path; }
    public long getDuration() { return duration; }
    public long getSize() { return size; }
    public long getDateAdded() { return dateAdded; }
    public String getMimeType() { return mimeType; }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(id);
        dest.writeString(name);
        dest.writeString(path);
        dest.writeLong(duration);
        dest.writeLong(size);
        dest.writeLong(dateAdded);
        dest.writeString(mimeType);
    }
}
