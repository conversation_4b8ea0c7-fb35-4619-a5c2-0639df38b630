package com.videoplayer.app.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.provider.MediaStore;
import com.videoplayer.app.model.VideoItem;
import java.util.ArrayList;
import java.util.List;

public class VideoManager {
    private Context context;

    public VideoManager(Context context) {
        this.context = context;
    }

    public List<VideoItem> getAllVideos() {
        List<VideoItem> videos = new ArrayList<>();
        ContentResolver contentResolver = context.getContentResolver();

        String[] projection = {
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.DATA,
            MediaStore.Video.Media.DURATION,
            MediaStore.Video.Media.SIZE,
            MediaStore.Video.Media.DATE_ADDED,
            MediaStore.Video.Media.MIME_TYPE
        };

        String selection = MediaStore.Video.Media.DURATION + " > ?";
        String[] selectionArgs = {"1000"}; // Videos longer than 1 second

        String sortOrder = MediaStore.Video.Media.DATE_ADDED + " DESC";

        Cursor cursor = contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            selectionArgs,
            sortOrder
        );

        if (cursor != null) {
            try {
                int idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID);
                int nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME);
                int pathColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA);
                int durationColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION);
                int sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE);
                int dateAddedColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATE_ADDED);
                int mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.MIME_TYPE);

                while (cursor.moveToNext()) {
                    long id = cursor.getLong(idColumn);
                    String name = cursor.getString(nameColumn);
                    String path = cursor.getString(pathColumn);
                    long duration = cursor.getLong(durationColumn);
                    long size = cursor.getLong(sizeColumn);
                    long dateAdded = cursor.getLong(dateAddedColumn);
                    String mimeType = cursor.getString(mimeTypeColumn);

                    if (name == null) name = "Unknown";
                    if (path != null && !path.isEmpty()) {
                        videos.add(new VideoItem(id, name, path, duration, size, dateAdded, mimeType));
                    }
                }
            } finally {
                cursor.close();
            }
        }

        return videos;
    }
}
