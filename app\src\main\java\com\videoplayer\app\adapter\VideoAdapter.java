package com.videoplayer.app.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.videoplayer.app.R;
import com.videoplayer.app.databinding.ItemVideoBinding;
import com.videoplayer.app.databinding.ItemVideoGridBinding;
import com.videoplayer.app.model.VideoItem;

public class VideoAdapter extends ListAdapter<VideoItem, VideoAdapter.VideoViewHolder> {

    public interface OnVideoClickListener {
        void onVideoClick(VideoItem video);
    }

    private OnVideoClickListener onVideoClickListener;
    private boolean isGridView = false;

    public VideoAdapter(OnVideoClickListener listener) {
        super(new VideoDiffCallback());
        this.onVideoClickListener = listener;
    }

    public void setGridView(boolean isGridView) {
        this.isGridView = isGridView;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (isGridView) {
            ItemVideoGridBinding gridBinding = ItemVideoGridBinding.inflate(
                LayoutInflater.from(parent.getContext()),
                parent,
                false
            );
            return new VideoViewHolder(gridBinding);
        } else {
            ItemVideoBinding binding = ItemVideoBinding.inflate(
                LayoutInflater.from(parent.getContext()),
                parent,
                false
            );
            return new VideoViewHolder(binding);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        holder.bind(getItem(position));
    }

    public class VideoViewHolder extends RecyclerView.ViewHolder {
        private ItemVideoBinding binding;
        private ItemVideoGridBinding gridBinding;
        private boolean isGrid;

        public VideoViewHolder(ItemVideoBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            this.isGrid = false;
        }

        public VideoViewHolder(ItemVideoGridBinding gridBinding) {
            super(gridBinding.getRoot());
            this.gridBinding = gridBinding;
            this.isGrid = true;
        }

        public void bind(VideoItem video) {
            if (isGrid) {
                bindGridView(video);
            } else {
                bindListView(video);
            }
        }

        private void bindListView(VideoItem video) {
            binding.textViewVideoName.setText(video.getName());
            binding.textViewDuration.setText(
                binding.getRoot().getContext().getString(
                    R.string.video_duration,
                    video.getFormattedDuration()
                )
            );
            binding.textViewSize.setText(
                binding.getRoot().getContext().getString(
                    R.string.video_size,
                    video.getFormattedSize()
                )
            );

            // Load video thumbnail using Glide
            Glide.with(binding.imageViewThumbnail.getContext())
                .load(video.getPath())
                .placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
                .centerCrop()
                .into(binding.imageViewThumbnail);

            binding.getRoot().setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video);
                }
            });
        }

        private void bindGridView(VideoItem video) {
            gridBinding.textViewTitle.setText(video.getDisplayName());
            gridBinding.textViewDuration.setText(video.getFormattedDuration());
            gridBinding.textViewSize.setText(video.getFormattedSize());

            // Load video thumbnail using Glide
            Glide.with(gridBinding.imageViewThumbnail.getContext())
                .load(video.getPath())
                .placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
                .centerCrop()
                .into(gridBinding.imageViewThumbnail);

            gridBinding.getRoot().setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video);
                }
            });
        }
    }

    private static class VideoDiffCallback extends DiffUtil.ItemCallback<VideoItem> {
        @Override
        public boolean areItemsTheSame(@NonNull VideoItem oldItem, @NonNull VideoItem newItem) {
            return oldItem.getId() == newItem.getId();
        }

        @Override
        public boolean areContentsTheSame(@NonNull VideoItem oldItem, @NonNull VideoItem newItem) {
            return oldItem.getName().equals(newItem.getName()) &&
                   oldItem.getPath().equals(newItem.getPath()) &&
                   oldItem.getDuration() == newItem.getDuration() &&
                   oldItem.getSize() == newItem.getSize();
        }
    }
}
