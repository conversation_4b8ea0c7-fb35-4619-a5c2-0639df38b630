package com.videoplayer.app.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.videoplayer.app.R;
import com.videoplayer.app.databinding.ItemVideoBinding;
import com.videoplayer.app.model.VideoItem;

public class VideoAdapter extends ListAdapter<VideoItem, VideoAdapter.VideoViewHolder> {

    public interface OnVideoClickListener {
        void onVideoClick(VideoItem video);
    }

    private OnVideoClickListener onVideoClickListener;

    public VideoAdapter(OnVideoClickListener listener) {
        super(new VideoDiffCallback());
        this.onVideoClickListener = listener;
    }

    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemVideoBinding binding = ItemVideoBinding.inflate(
            LayoutInflater.from(parent.getContext()),
            parent,
            false
        );
        return new VideoViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        holder.bind(getItem(position));
    }

    public class VideoViewHolder extends RecyclerView.ViewHolder {
        private ItemVideoBinding binding;

        public VideoViewHolder(ItemVideoBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        public void bind(VideoItem video) {
            binding.textViewVideoName.setText(video.getDisplayName());
            binding.textViewDuration.setText(
                binding.getRoot().getContext().getString(
                    R.string.video_duration,
                    video.getFormattedDuration()
                )
            );
            binding.textViewSize.setText(
                binding.getRoot().getContext().getString(
                    R.string.video_size,
                    video.getFormattedSize()
                )
            );

            // Load video thumbnail using Glide
            Glide.with(binding.imageViewThumbnail.getContext())
                .load(video.getPath())
                .placeholder(R.drawable.ic_launcher_background)
                .error(R.drawable.ic_launcher_background)
                .centerCrop()
                .into(binding.imageViewThumbnail);

            binding.getRoot().setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video);
                }
            });
        }
    }

    private static class VideoDiffCallback extends DiffUtil.ItemCallback<VideoItem> {
        @Override
        public boolean areItemsTheSame(@NonNull VideoItem oldItem, @NonNull VideoItem newItem) {
            return oldItem.getId() == newItem.getId();
        }

        @Override
        public boolean areContentsTheSame(@NonNull VideoItem oldItem, @NonNull VideoItem newItem) {
            return oldItem.getName().equals(newItem.getName()) &&
                   oldItem.getPath().equals(newItem.getPath()) &&
                   oldItem.getDuration() == newItem.getDuration() &&
                   oldItem.getSize() == newItem.getSize();
        }
    }
}
