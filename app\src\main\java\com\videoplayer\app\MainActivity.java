package com.videoplayer.app;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.videoplayer.app.adapter.VideoAdapter;
import com.videoplayer.app.databinding.ActivityMainBinding;
import com.videoplayer.app.model.VideoItem;
import com.videoplayer.app.utils.VideoManager;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;
    private VideoAdapter videoAdapter;
    private VideoManager videoManager;
    private ExecutorService executor;
    private List<VideoItem> allVideos = new ArrayList<>();
    private List<VideoItem> filteredVideos = new ArrayList<>();

    private final ActivityResultLauncher<String> requestPermissionLauncher =
            registerForActivityResult(new ActivityResultContracts.RequestPermission(), isGranted -> {
                if (isGranted) {
                    loadVideos();
                } else {
                    showNoPermissionMessage();
                }
            });

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupRecyclerView();
        setupSearchView();
        videoManager = new VideoManager(this);
        executor = Executors.newSingleThreadExecutor();

        checkPermissionAndLoadVideos();
    }

    private void setupRecyclerView() {
        videoAdapter = new VideoAdapter(this::openVideoPlayer);

        binding.recyclerViewVideos.setAdapter(videoAdapter);
        binding.recyclerViewVideos.setLayoutManager(new LinearLayoutManager(this));
    }

    private void setupSearchView() {
        binding.searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                filterVideos(query);
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                filterVideos(newText);
                return true;
            }
        });
    }



    private void filterVideos(String query) {
        filteredVideos.clear();
        if (TextUtils.isEmpty(query)) {
            filteredVideos.addAll(allVideos);
        } else {
            String lowerCaseQuery = query.toLowerCase();
            for (VideoItem video : allVideos) {
                if (video.getDisplayName().toLowerCase().contains(lowerCaseQuery) ||
                    video.getName().toLowerCase().contains(lowerCaseQuery)) {
                    filteredVideos.add(video);
                }
            }
        }

        // Update the adapter with filtered results
        videoAdapter.submitList(new ArrayList<>(filteredVideos));

        // Show/hide no results message
        if (filteredVideos.isEmpty() && !TextUtils.isEmpty(query)) {
            showNoSearchResults();
        } else if (filteredVideos.isEmpty() && allVideos.isEmpty()) {
            showNoVideosMessage();
        } else {
            binding.recyclerViewVideos.setVisibility(View.VISIBLE);
            binding.textViewNoVideos.setVisibility(View.GONE);
        }
    }

    private void showNoSearchResults() {
        binding.recyclerViewVideos.setVisibility(View.GONE);
        binding.textViewNoVideos.setVisibility(View.VISIBLE);
        binding.textViewNoVideos.setText(getString(R.string.no_search_results));
    }

    private void checkPermissionAndLoadVideos() {
        String permission = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU 
            ? Manifest.permission.READ_MEDIA_VIDEO 
            : Manifest.permission.READ_EXTERNAL_STORAGE;

        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            loadVideos();
        } else {
            requestPermissionLauncher.launch(permission);
        }
    }

    private void loadVideos() {
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.textViewNoVideos.setVisibility(View.GONE);
        
        executor.execute(() -> {
            try {
                List<VideoItem> videos = videoManager.getAllVideos();
                
                runOnUiThread(() -> {
                    binding.progressBar.setVisibility(View.GONE);
                    if (videos.isEmpty()) {
                        showNoVideosMessage();
                    } else {
                        allVideos.clear();
                        allVideos.addAll(videos);
                        filteredVideos.clear();
                        filteredVideos.addAll(videos);
                        showVideos(filteredVideos);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    binding.progressBar.setVisibility(View.GONE);
                    showNoVideosMessage();
                });
            }
        });
    }

    private void showVideos(List<VideoItem> videos) {
        binding.recyclerViewVideos.setVisibility(View.VISIBLE);
        binding.textViewNoVideos.setVisibility(View.GONE);
        videoAdapter.submitList(videos);
    }

    private void showNoVideosMessage() {
        binding.recyclerViewVideos.setVisibility(View.GONE);
        binding.textViewNoVideos.setVisibility(View.VISIBLE);
        binding.textViewNoVideos.setText(getString(R.string.no_videos_found));
    }

    private void showNoPermissionMessage() {
        binding.recyclerViewVideos.setVisibility(View.GONE);
        binding.textViewNoVideos.setVisibility(View.VISIBLE);
        binding.textViewNoVideos.setText(getString(R.string.permission_required));
    }

    private void openVideoPlayer(VideoItem video) {
        Intent intent = new Intent(this, VideoPlayerActivity.class);
        intent.putExtra(VideoPlayerActivity.EXTRA_VIDEO_ITEM, video);
        startActivity(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null) {
            executor.shutdown();
        }
    }
}
