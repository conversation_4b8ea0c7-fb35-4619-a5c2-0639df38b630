<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_video_player" modulePackage="com.videoplayer.app" filePath="app\src\main\res\layout\activity_video_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_video_player_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="44" endOffset="51"/></Target><Target id="@+id/playerView" view="com.google.android.exoplayer2.ui.PlayerView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="31"/></Target><Target id="@+id/progressBarLoading" view="ProgressBar"><Expressions/><location startLine="21" startOffset="4" endLine="29" endOffset="51"/></Target><Target id="@+id/textViewError" view="TextView"><Expressions/><location startLine="31" startOffset="4" endLine="42" endOffset="51"/></Target></Targets></Layout>