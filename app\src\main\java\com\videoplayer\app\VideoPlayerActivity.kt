package com.videoplayer.app

import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.PlaybackException
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.MediaSource
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.videoplayer.app.databinding.ActivityVideoPlayerBinding
import com.videoplayer.app.model.VideoItem

class VideoPlayerActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_VIDEO_ITEM = "extra_video_item"
    }

    private lateinit var binding: ActivityVideoPlayerBinding
    private var exoPlayer: ExoPlayer? = null
    private var videoItem: VideoItem? = null
    private var playbackPosition = 0L
    private var playWhenReady = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVideoPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Keep screen on during video playback
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        videoItem = intent.getParcelableExtra(EXTRA_VIDEO_ITEM)
        
        if (videoItem == null) {
            finish()
            return
        }

        setupPlayer()
    }

    private fun setupPlayer() {
        exoPlayer = ExoPlayer.Builder(this).build().also { player ->
            binding.playerView.player = player
            
            val mediaItem = MediaItem.fromUri(Uri.parse(videoItem!!.path))
            val mediaSource = buildMediaSource(mediaItem)
            
            player.setMediaSource(mediaSource)
            player.playWhenReady = playWhenReady
            player.seekTo(playbackPosition)
            player.prepare()

            player.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_BUFFERING -> {
                            binding.progressBarLoading.visibility = View.VISIBLE
                        }
                        Player.STATE_READY -> {
                            binding.progressBarLoading.visibility = View.GONE
                            binding.textViewError.visibility = View.GONE
                        }
                        Player.STATE_ENDED -> {
                            // Video ended, you can add logic here
                        }
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    binding.progressBarLoading.visibility = View.GONE
                    binding.textViewError.visibility = View.VISIBLE
                }
            })
        }
    }

    private fun buildMediaSource(mediaItem: MediaItem): MediaSource {
        val dataSourceFactory = DefaultDataSource.Factory(this)
        return ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(mediaItem)
    }

    private fun releasePlayer() {
        exoPlayer?.let { player ->
            playbackPosition = player.currentPosition
            playWhenReady = player.playWhenReady
            player.release()
        }
        exoPlayer = null
    }

    override fun onStart() {
        super.onStart()
        if (exoPlayer == null) {
            setupPlayer()
        }
    }

    override fun onResume() {
        super.onResume()
        if (exoPlayer == null) {
            setupPlayer()
        }
    }

    override fun onPause() {
        super.onPause()
        releasePlayer()
    }

    override fun onStop() {
        super.onStop()
        releasePlayer()
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }
}
