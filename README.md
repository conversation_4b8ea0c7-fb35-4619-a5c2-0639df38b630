# تطبيق مشغل الفيديو - Android Video Player

تطبيق Android بسيط وفعال لتشغيل الفيديوهات المحفوظة على الجهاز.

## الميزات

- 📱 عرض جميع الفيديوهات المحفوظة على الجهاز
- ▶️ تشغيل الفيديوهات بجودة عالية باستخدام ExoPlayer
- 🎬 عرض معلومات الفيديو (المدة، الحجم، الاسم)
- 🖼️ عرض صور مصغرة للفيديوهات
- 📱 واجهة مستخدم عربية بتصميم Material Design
- 🔄 دعم تنسيقات فيديو متعددة
- 🔒 طلب الصلاحيات المطلوبة تلقائياً

## المتطلبات

- Android 7.0 (API level 24) أو أحدث
- مساحة تخزين كافية للفيديوهات
- صلاحية الوصول للملفات

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **ExoPlayer** - لتشغيل الفيديوهات
- **Material Design** - للتصميم
- **RecyclerView** - لعرض قائمة الفيديوهات
- **Glide** - لتحميل الصور المصغرة
- **ViewBinding** - لربط العناصر

## كيفية البناء والتشغيل

1. **استنساخ المشروع:**
   ```bash
   git clone <repository-url>
   cd VideoPlayer
   ```

2. **فتح المشروع في Android Studio:**
   - افتح Android Studio
   - اختر "Open an existing project"
   - حدد مجلد المشروع

3. **بناء التطبيق:**
   ```bash
   ./gradlew assembleDebug
   ```

4. **تشغيل التطبيق:**
   - اربط جهاز Android أو استخدم محاكي
   - اضغط على "Run" في Android Studio

## هيكل المشروع

```
app/
├── src/main/
│   ├── java/com/videoplayer/app/
│   │   ├── MainActivity.kt              # الشاشة الرئيسية
│   │   ├── VideoPlayerActivity.kt       # شاشة تشغيل الفيديو
│   │   ├── adapter/
│   │   │   └── VideoAdapter.kt          # محول قائمة الفيديوهات
│   │   ├── model/
│   │   │   └── VideoItem.kt             # نموذج بيانات الفيديو
│   │   └── utils/
│   │       └── VideoManager.kt          # إدارة الفيديوهات
│   ├── res/
│   │   ├── layout/                      # ملفات التخطيط
│   │   ├── values/                      # الألوان والنصوص
│   │   └── xml/                         # إعدادات النسخ الاحتياطي
│   └── AndroidManifest.xml              # إعدادات التطبيق
```

## الصلاحيات المطلوبة

- `READ_EXTERNAL_STORAGE` - للوصول للفيديوهات (Android 12 وأقل)
- `READ_MEDIA_VIDEO` - للوصول للفيديوهات (Android 13 وأحدث)
- `INTERNET` - لتحميل الصور المصغرة
- `ACCESS_NETWORK_STATE` - للتحقق من حالة الشبكة

## كيفية الاستخدام

1. **تشغيل التطبيق:** افتح التطبيق من قائمة التطبيقات
2. **منح الصلاحيات:** اقبل صلاحية الوصول للملفات عند الطلب
3. **تصفح الفيديوهات:** ستظهر قائمة بجميع الفيديوهات المحفوظة
4. **تشغيل فيديو:** اضغط على أي فيديو لتشغيله
5. **التحكم في التشغيل:** استخدم عناصر التحكم لإيقاف/تشغيل الفيديو

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
