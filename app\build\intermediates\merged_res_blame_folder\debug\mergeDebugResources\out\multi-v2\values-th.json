{"logs": [{"outputFile": "com.videoplayer.app-mergeDebugResources-38:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f1b32c0fc18016c5a3753e707e390b71\\transformed\\core-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "55,56,57,58,59,60,61,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3964,4060,4163,4261,4359,4462,4567,13441", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "4055,4158,4256,4354,4457,4562,4674,13537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f6658a502d56b73d797878a8e950660d\\transformed\\exoplayer-core-2.19.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6955,7031,7094,7162,7230,7307,7380,7471,7557", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "7026,7089,7157,7225,7302,7375,7466,7552,7631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74c8f557fa6abfe04d4cbe84bc441f56\\transformed\\exoplayer-ui-2.19.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3114,3173,3239,3306,3373,3427,3490,3572,3649", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3109,3168,3234,3301,3368,3422,3485,3567,3644,3698"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,573,5041,5122,5201,5280,5368,5458,5529,5593,5684,5775,5839,5902,5967,6038,6147,6252,6365,6433,6516,6589,6660,6745,6828,6891,7636,7689,7747,7795,7856,7915,7983,8049,8117,8180,8239,8305,8372,8439,8493,8556,8638,8715", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,62,58,65,66,66,53,62,81,76,53", "endOffsets": "373,568,746,5117,5196,5275,5363,5453,5524,5588,5679,5770,5834,5897,5962,6033,6142,6247,6360,6428,6511,6584,6655,6740,6823,6886,6950,7684,7742,7790,7851,7910,7978,8044,8112,8175,8234,8300,8367,8434,8488,8551,8633,8710,8764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2fd47ea89dff03665557e3b19b314124\\transformed\\material-1.10.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2785,2877,2955,3009,3062,3128,3198,3276,3362,3442,3514,3592,3661,3730,3828,3910,3998,4091,4185,4259,4328,4423,4475,4558,4626,4711,4799,4861,4925,4988,5058,5158,5254,5351,5444,5502,5559", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2780,2872,2950,3004,3057,3123,3193,3271,3357,3437,3509,3587,3656,3725,3823,3905,3993,4086,4180,4254,4323,4418,4470,4553,4621,4706,4794,4856,4920,4983,5053,5153,5249,5346,5439,5497,5554,5631"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "751,3565,3638,3710,3793,3878,4679,4778,4891,4971,8769,8859,8929,8989,9076,9142,9207,9268,9332,9393,9447,9548,9609,9669,9723,9793,9904,9991,10072,10215,10294,10376,10508,10600,10678,10732,10785,10851,10921,10999,11085,11165,11237,11315,11384,11453,11551,11633,11721,11814,11908,11982,12051,12146,12198,12281,12349,12434,12522,12584,12648,12711,12781,12881,12977,13074,13167,13225,13282", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "931,3633,3705,3788,3873,3959,4773,4886,4966,5036,8854,8924,8984,9071,9137,9202,9263,9327,9388,9442,9543,9604,9664,9718,9788,9899,9986,10067,10210,10289,10371,10503,10595,10673,10727,10780,10846,10916,10994,11080,11160,11232,11310,11379,11448,11546,11628,11716,11809,11903,11977,12046,12141,12193,12276,12344,12429,12517,12579,12643,12706,12776,12876,12972,13069,13162,13220,13277,13354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d5e0ffba3d1002a1fc6a067c8f700aca\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "936,1041,1134,1242,1327,1429,1539,1617,1694,1785,1878,1969,2063,2163,2256,2351,2445,2536,2627,2708,2811,2909,3007,3110,3216,3317,3470,13359", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "1036,1129,1237,1322,1424,1534,1612,1689,1780,1873,1964,2058,2158,2251,2346,2440,2531,2622,2703,2806,2904,3002,3105,3211,3312,3465,3560,13436"}}]}]}