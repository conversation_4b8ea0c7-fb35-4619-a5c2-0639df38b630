<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="gray">#FF808080</color>
    <color name="light_gray">#FFE0E0E0</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">مشغل الفيديو</string>
    <string name="error_playing_video">خطأ في تشغيل الفيديو</string>
    <string name="exit_fullscreen">خروج من ملء الشاشة</string>
    <string name="fullscreen">ملء الشاشة</string>
    <string name="grant_permission">منح الصلاحية</string>
    <string name="loading">جاري التحميل...</string>
    <string name="no_videos_found">لم يتم العثور على فيديوهات</string>
    <string name="pause">إيقاف مؤقت</string>
    <string name="permission_required">صلاحية الوصول للملفات مطلوبة</string>
    <string name="play">تشغيل</string>
    <string name="video_duration">مدة الفيديو: %s</string>
    <string name="video_size">حجم الملف: %s</string>
    <style name="Theme.VideoPlayer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.VideoPlayer.Fullscreen" parent="Theme.VideoPlayer">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>